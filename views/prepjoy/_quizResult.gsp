<!-------- RESULT UI --------->



<script>
    const resultData = getResultData();
    const skippedQuestions = getSkippedQuestions(resultData);
    const correctQuestions = getCorrectQuestions(resultData);
    const inCorrectQuestions = getInCorrectQuestions(resultData);

    function getResultData() {
        const questionMap = new Map();
        questions.forEach(q => questionMap.set(q.id, q));

        const statsMap = new Map();
        quizStatisticsList.forEach(s => statsMap.set(s.objId, s));

        const userMap = new Map();
        qaAnswers.forEach(u => userMap.set(u.id, u));

        return questions.map(q => {
            const key = String(q.id); // use string key
            const stat = statsMap.get(key) || {};
            const user = userMap.get(key) || {};
            return { ...q, ...stat, ...user };
        });
    }

    function getSkippedQuestions(data){
        let result = new Array(data.length);
        let count = 0;

        for (let i = 0; i < data.length; i++) {
            if (data[i].userOption === "-1") {
                result[count++] = data[i];
            }
        }
        result.length = count;
        return result
    }

    function getCorrectQuestions(data){
        const result = [];
        for (let i = 0; i < data.length; i++) {
            const item = data[i];
            if (item.userOption !== "-1" && item.userOption === item.correctOption) {
                result.push(item);
            }
        }
        return result
    }

    function getInCorrectQuestions(data){
        const result = [];
        for (let i = 0; i < data.length; i++) {
            const item = data[i];
            if (item.userOption !== "-1" && item.userOption !== item.correctOption) {
                result.push(item);
            }
        }
        return result
    }
</script>