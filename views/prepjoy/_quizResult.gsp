<!-------- RESULT UI --------->

<div class="quiz-result-container" style="padding: 20px; background-color: #f8f9fa; min-height: 100vh;">
    <div class="container">
        <!-- Overall Summary Section -->
        <div class="overall-summary-section" style="margin-bottom: 30px;">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h3 style="font-weight: 600; color: #333; margin: 0;">Overall Summary</h3>
                <button class="btn btn-warning btn-sm" style="background-color: #b8c832; border: none; color: white; padding: 8px 16px; border-radius: 20px; font-weight: 500;">
                    Solutions
                </button>
            </div>

            <div class="overall-stats-grid" style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 20px; margin-bottom: 20px;">
                <div class="stat-card" style="background: white; padding: 20px; border-radius: 10px; text-align: center; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                    <div class="stat-value" style="font-size: 24px; font-weight: bold; color: #333; margin-bottom: 5px;" id="overall-score">
                        --/--
                    </div>
                    <div class="stat-label" style="color: #666; font-size: 14px;">Score</div>
                </div>

                <div class="stat-card" style="background: white; padding: 20px; border-radius: 10px; text-align: center; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                    <div class="stat-value" style="font-size: 24px; font-weight: bold; color: #333; margin-bottom: 5px;" id="overall-attempted">
                        --/--
                    </div>
                    <div class="stat-label" style="color: #666; font-size: 14px;">Attempted</div>
                </div>

                <div class="stat-card" style="background: white; padding: 20px; border-radius: 10px; text-align: center; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                    <div class="stat-value" style="font-size: 24px; font-weight: bold; color: #333; margin-bottom: 5px;" id="overall-accuracy">
                        --%
                    </div>
                    <div class="stat-label" style="color: #666; font-size: 14px;">Accuracy</div>
                </div>

                <div class="stat-card" style="background: white; padding: 20px; border-radius: 10px; text-align: center; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                    <div class="stat-value" style="font-size: 24px; font-weight: bold; color: #333; margin-bottom: 5px;" id="overall-time">
                        --m --s
                    </div>
                    <div class="stat-label" style="color: #666; font-size: 14px;">Time Taken</div>
                </div>
            </div>
        </div>

        <!-- Sectional Summary Section -->
        <div class="sectional-summary-section" style="margin-bottom: 30px;">
            <h3 style="font-weight: 600; color: #333; margin-bottom: 20px;">Sectional Summary</h3>

            <div class="sectional-table-container" style="background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                <table class="table table-striped mb-0" style="margin: 0;">
                    <thead style="background-color: #f8f9fa;">
                        <tr>
                            <th style="padding: 15px; font-weight: 600; color: #333; border: none;">Section</th>
                            <th style="padding: 15px; font-weight: 600; color: #333; border: none; text-align: center;">Score</th>
                            <th style="padding: 15px; font-weight: 600; color: #333; border: none; text-align: center;">Attempted</th>
                            <th style="padding: 15px; font-weight: 600; color: #333; border: none; text-align: center;">Accuracy</th>
                            <th style="padding: 15px; font-weight: 600; color: #333; border: none; text-align: center;">Time</th>
                        </tr>
                    </thead>
                    <tbody id="sectional-summary-tbody">
                        <!-- Dynamic content will be inserted here -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Question Distribution Section -->
        <div class="question-distribution-section">
            <h3 style="font-weight: 600; color: #333; margin-bottom: 20px;">Question Distribution</h3>

            <div class="distribution-stats" style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 20px; margin-bottom: 20px;">
                <div class="distribution-card" style="background: white; padding: 20px; border-radius: 10px; text-align: center; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                    <div class="distribution-value" style="font-size: 24px; font-weight: bold; color: #28a745; margin-bottom: 5px;" id="correct-count">
                        --
                    </div>
                    <div class="distribution-label" style="color: #666; font-size: 14px;">Correct</div>
                </div>

                <div class="distribution-card" style="background: white; padding: 20px; border-radius: 10px; text-align: center; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                    <div class="distribution-value" style="font-size: 24px; font-weight: bold; color: #dc3545; margin-bottom: 5px;" id="incorrect-count">
                        --
                    </div>
                    <div class="distribution-label" style="color: #666; font-size: 14px;">Incorrect</div>
                </div>

                <div class="distribution-card" style="background: white; padding: 20px; border-radius: 10px; text-align: center; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                    <div class="distribution-value" style="font-size: 24px; font-weight: bold; color: #ffc107; margin-bottom: 5px;" id="skipped-count">
                        --
                    </div>
                    <div class="distribution-label" style="color: #666; font-size: 14px;">Skipped</div>
                </div>
            </div>

            <!-- Progress Bar -->
            <div class="distribution-progress" style="background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                <div class="progress" style="height: 20px; border-radius: 10px; background-color: #e9ecef;">
                    <div class="progress-bar bg-success" role="progressbar" style="width: 0%; border-radius: 10px;" id="correct-progress"></div>
                    <div class="progress-bar bg-danger" role="progressbar" style="width: 0%;" id="incorrect-progress"></div>
                    <div class="progress-bar bg-warning" role="progressbar" style="width: 0%;" id="skipped-progress"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    const resultData = getResultData();
    const skippedQuestions = getSkippedQuestions(resultData);
    const correctQuestions = getCorrectQuestions(resultData);
    const inCorrectQuestions = getInCorrectQuestions(resultData);

    function getResultData() {
        const questionMap = new Map();
        questions.forEach(q => questionMap.set(q.id, q));

        const statsMap = new Map();
        quizStatisticsList.forEach(s => statsMap.set(s.objId, s));

        const userMap = new Map();
        qaAnswers.forEach(u => userMap.set(u.id, u));

        return questions.map(q => {
            const key = String(q.id); // use string key
            const stat = statsMap.get(key) || {};
            const user = userMap.get(key) || {};
            return { ...q, ...stat, ...user };
        });
    }

    function getSkippedQuestions(data){
        let result = new Array(data.length);
        let count = 0;

        for (let i = 0; i < data.length; i++) {
            if (data[i].userOption === "-1") {
                result[count++] = data[i];
            }
        }
        result.length = count;
        return result
    }

    function getCorrectQuestions(data){
        const result = [];
        for (let i = 0; i < data.length; i++) {
            const item = data[i];
            if (item.userOption !== "-1" && item.userOption === item.correctOption) {
                result.push(item);
            }
        }
        return result
    }

    function getInCorrectQuestions(data){
        const result = [];
        for (let i = 0; i < data.length; i++) {
            const item = data[i];
            if (item.userOption !== "-1" && item.userOption !== item.correctOption) {
                result.push(item);
            }
        }
        return result
    }

    // Calculate overall statistics
    function calculateOverallStats() {
        const totalQuestions = questions.length;
        const correctCount = correctQuestions.length;
        const incorrectCount = inCorrectQuestions.length;
        const skippedCount = skippedQuestions.length;
        const attemptedCount = correctCount + incorrectCount;
        const accuracy = attemptedCount > 0 ? Math.round((correctCount / attemptedCount) * 100) : 0;

        // Calculate total time (if available)
        let totalTime = 0;
        if (typeof userTotalTime !== 'undefined' && userTotalTime) {
            totalTime = userTotalTime;
        } else {
            // Fallback: sum up individual question times
            resultData.forEach(q => {
                if (q.userTime) {
                    totalTime += parseFloat(q.userTime);
                }
            });
        }

        const minutes = Math.floor(totalTime / 60);
        const seconds = Math.floor(totalTime % 60);

        return {
            score: correctCount + "/" + totalQuestions,
            attempted: attemptedCount + "/" + totalQuestions,
            accuracy: accuracy + "%",
            time: minutes + "m " + seconds + "s",
            correctCount,
            incorrectCount,
            skippedCount,
            totalQuestions
        };
    }

    // Calculate sectional statistics
    function calculateSectionalStats() {
        const sectionStats = {};

        resultData.forEach(q => {
            const section = q.subject || 'General';
            if (!sectionStats[section]) {
                sectionStats[section] = {
                    total: 0,
                    correct: 0,
                    incorrect: 0,
                    skipped: 0,
                    totalTime: 0
                };
            }

            sectionStats[section].total++;

            if (q.userOption === "-1") {
                sectionStats[section].skipped++;
            } else if (q.userOption === q.correctOption) {
                sectionStats[section].correct++;
            } else {
                sectionStats[section].incorrect++;
            }

            if (q.userTime) {
                sectionStats[section].totalTime += parseFloat(q.userTime);
            }
        });

        // Convert to array format for table display
        const sectionalArray = [];
        Object.keys(sectionStats).forEach(section => {
            const stats = sectionStats[section];
            const attempted = stats.correct + stats.incorrect;
            const accuracy = attempted > 0 ? Math.round((stats.correct / attempted) * 100) : 0;
            const minutes = Math.floor(stats.totalTime / 60);
            const seconds = Math.floor(stats.totalTime % 60);

            sectionalArray.push({
                section: section,
                score: stats.correct,
                attempted: attempted,
                accuracy: accuracy + "%",
                time: minutes + "m"
            });
        });

        return sectionalArray;
    }

    // Populate the UI with calculated data
    function populateQuizResultUI() {
        const overallStats = calculateOverallStats();
        const sectionalStats = calculateSectionalStats();

        // Update overall summary
        document.getElementById('overall-score').textContent = overallStats.score;
        document.getElementById('overall-attempted').textContent = overallStats.attempted;
        document.getElementById('overall-accuracy').textContent = overallStats.accuracy;
        document.getElementById('overall-time').textContent = overallStats.time;

        // Update question distribution
        document.getElementById('correct-count').textContent = overallStats.correctCount;
        document.getElementById('incorrect-count').textContent = overallStats.incorrectCount;
        document.getElementById('skipped-count').textContent = overallStats.skippedCount;

        // Update progress bar
        const total = overallStats.totalQuestions;
        const correctPercent = (overallStats.correctCount / total) * 100;
        const incorrectPercent = (overallStats.incorrectCount / total) * 100;
        const skippedPercent = (overallStats.skippedCount / total) * 100;

        document.getElementById('correct-progress').style.width = correctPercent + "%";
        document.getElementById('incorrect-progress').style.width = incorrectPercent + "%";
        document.getElementById('skipped-progress').style.width = skippedPercent + "%";

        // Update sectional summary table
        const tbody = document.getElementById('sectional-summary-tbody');
        tbody.innerHTML = '';

        sectionalStats.forEach(section => {
            const row = document.createElement('tr');
            row.innerHTML =
                "<td style='padding: 15px; border: none; font-weight: 500;'>" + section.section + "</td>" +
                "<td style='padding: 15px; border: none; text-align: center;'>" + section.score + "</td>" +
                "<td style='padding: 15px; border: none; text-align: center;'>" + section.attempted + "</td>" +
                "<td style='padding: 15px; border: none; text-align: center;'>" + section.accuracy + "</td>" +
                "<td style='padding: 15px; border: none; text-align: center;'>" + section.time + "</td>";
            tbody.appendChild(row);
        });
    }

    // Initialize the UI when the page loads
    document.addEventListener('DOMContentLoaded', function() {
        populateQuizResultUI();
    });

    // Also call immediately in case DOMContentLoaded has already fired
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', populateQuizResultUI);
    } else {
        populateQuizResultUI();
    }
</script>