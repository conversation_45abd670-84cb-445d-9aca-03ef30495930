<!-------- RESULT UI --------->
<style>
    .quiz-result-container {
        padding: 20px;
        background-color: #f8f9fa;
        min-height: 100vh;
    }

    .overall-summary-section {
        margin-bottom: 30px;
    }

    .overall-summary-section h3 {
        font-weight: 600;
        color: #333;
        margin: 0;
    }

    .solutions-btn {
        background-color: #b8c832 !important;
        border: none !important;
        color: #000;
        padding: 8px 16px !important;
        border-radius: 20px !important;
        font-weight: 500 !important;
    }

    .overall-stats-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 20px;
        margin-bottom: 20px;
    }

    .stat-card {
        background: white;
        padding: 20px;
        border-radius: 10px;
        text-align: center;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .stat-value {
        font-size: 24px;
        font-weight: bold;
        color: #333;
        margin-bottom: 5px;
    }

    .stat-label {
        color: #666;
        font-size: 14px;
    }

    .sectional-summary-section {
        margin-bottom: 30px;
    }

    .sectional-summary-section h3 {
        font-weight: 600;
        color: #333;
        margin-bottom: 20px;
    }

    .sectional-table-container {
        background: white;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .sectional-table-container table {
        margin: 0;
    }

    .sectional-table-container thead {
        background-color: #f8f9fa;
    }

    .sectional-table-container th {
        padding: 15px;
        font-weight: 600;
        color: #333;
        border: none;
    }

    .sectional-table-container th.text-center {
        text-align: center;
    }

    .sectional-table-container td {
        padding: 15px;
        border: none;
    }

    .sectional-table-container td.text-center {
        text-align: center;
    }

    .sectional-table-container td.section-name {
        font-weight: 500;
    }

    .question-distribution-section h3 {
        font-weight: 600;
        color: #333;
        margin-bottom: 20px;
    }

    .distribution-stats {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 20px;
        margin-bottom: 20px;
    }

    .distribution-card {
        background: white;
        padding: 20px;
        border-radius: 10px;
        text-align: center;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .distribution-value {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 5px;
    }

    .distribution-value.correct {
        color: #28a745;
    }

    .distribution-value.incorrect {
        color: #dc3545;
    }

    .distribution-value.skipped {
        color: #ffc107;
    }

    .distribution-label {
        color: #666;
        font-size: 14px;
    }

    .distribution-progress {
        background: white;
        padding: 20px;
        border-radius: 10px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .distribution-progress .progress {
        height: 20px;
        border-radius: 10px;
        background-color: #e9ecef;
    }

    .distribution-progress .progress-bar:first-child {
        border-radius: 10px;
    }

    .solutions-section {
        display: none;
    }

    .solutions-section.active {
        display: block;
    }

    .quiz-summary-sections.hidden {
        display: none;
    }

    .back-to-summary-btn {
        background-color: #6c757d !important;
        border: none !important;
        color: white !important;
        padding: 8px 16px !important;
        border-radius: 20px !important;
        font-weight: 500 !important;
        margin-bottom: 20px !important;
    }

    @media (max-width: 768px) {
        .overall-stats-grid {
            grid-template-columns: repeat(2, 1fr);
        }

        .distribution-stats {
            grid-template-columns: 1fr;
        }
    }
</style>
<div class="quiz-result-container">
    <div class="container">
        <!-- Quiz Summary Sections -->
        <div class="quiz-summary-sections">
            <!-- Overall Summary Section -->
            <div class="overall-summary-section">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h3>Overall Summary</h3>
                    <button class="btn btn-warning btn-sm solutions-btn" onclick="showSolutions()">
                        Solutions
                    </button>
                </div>

            <div class="overall-stats-grid">
                <div class="stat-card">
                    <div class="stat-value" id="overall-score">
                        --/--
                    </div>
                    <div class="stat-label">Score</div>
                </div>

                <div class="stat-card">
                    <div class="stat-value" id="overall-attempted">
                        --/--
                    </div>
                    <div class="stat-label">Attempted</div>
                </div>

                <div class="stat-card">
                    <div class="stat-value" id="overall-accuracy">
                        --%
                    </div>
                    <div class="stat-label">Accuracy</div>
                </div>

                <div class="stat-card">
                    <div class="stat-value" id="overall-time">
                        --m --s
                    </div>
                    <div class="stat-label">Time Taken</div>
                </div>
            </div>
        </div>

        <!-- Sectional Summary Section -->
        <div class="sectional-summary-section">
            <h3>Sectional Summary</h3>

            <div class="sectional-table-container">
                <table class="table table-striped mb-0">
                    <thead>
                        <tr>
                            <th>Section</th>
                            <th class="text-center">Score</th>
                            <th class="text-center">Attempted</th>
                            <th class="text-center">Accuracy</th>
                            <th class="text-center">Time</th>
                        </tr>
                    </thead>
                    <tbody id="sectional-summary-tbody">
                        <!-- Dynamic content will be inserted here -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Question Distribution Section -->
        <div class="question-distribution-section">
            <h3>Question Distribution</h3>

            <div class="distribution-stats">
                <div class="distribution-card">
                    <div class="distribution-value correct" id="correct-count">
                        --
                    </div>
                    <div class="distribution-label">Correct</div>
                </div>

                <div class="distribution-card">
                    <div class="distribution-value incorrect" id="incorrect-count">
                        --
                    </div>
                    <div class="distribution-label">Incorrect</div>
                </div>

                <div class="distribution-card">
                    <div class="distribution-value skipped" id="skipped-count">
                        --
                    </div>
                    <div class="distribution-label">Skipped</div>
                </div>
            </div>

            <!-- Progress Bar -->
            <div class="distribution-progress">
                <div class="progress">
                    <div class="progress-bar bg-success" role="progressbar" id="correct-progress"></div>
                    <div class="progress-bar bg-danger" role="progressbar" id="incorrect-progress"></div>
                    <div class="progress-bar bg-warning" role="progressbar" id="skipped-progress"></div>
                </div>
            </div>
        </div>

        <!-- Solutions Section -->
        <div class="solutions-section">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h3>Solutions</h3>
                <button class="btn btn-secondary btn-sm back-to-summary-btn" onclick="showSummary()">
                    Back to Summary
                </button>
            </div>

            <div class="solutions-content">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    Solutions will be displayed here. This section will show detailed explanations for each question.
                </div>

                <!-- Solutions content will be dynamically loaded here -->
                <div id="solutions-list">
                    <!-- Dynamic solutions content -->
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    let resultData = getResultData();
    let skippedQuestions = getSkippedQuestions(resultData);
    let correctQuestions = getCorrectQuestions(resultData);
    let inCorrectQuestions = getInCorrectQuestions(resultData);

    function getResultData() {
        const userResponseObj = JSON.parse(qaObj);
        const questionMap = new Map();
        questions.forEach(q => questionMap.set(q.id, q));

        const statsMap = new Map();
        quizStatisticsList.forEach(s => statsMap.set(s.objId, s));

        const userMap = new Map();
        userResponseObj.userAnswers.forEach(u => userMap.set(u.id, u));

        return questions.map(q => {
            const key = String(q.id); // use string key
            const stat = statsMap.get(key) || {};
            const user = userMap.get(key) || {};
            return { ...q, ...stat, ...user };
        });
    }

    function getSkippedQuestions(data){
        let result = new Array(data.length);
        let count = 0;

        for (let i = 0; i < data.length; i++) {
            if (data[i].userOption === "-1") {
                result[count++] = data[i];
            }
        }
        result.length = count;
        return result
    }

    function getCorrectQuestions(data){
        const result = [];
        for (let i = 0; i < data.length; i++) {
            const item = data[i];
            if (item.userOption !== "-1" && item.userOption === item.correctOption) {
                result.push(item);
            }
        }
        return result
    }

    function getInCorrectQuestions(data){
        const result = [];
        for (let i = 0; i < data.length; i++) {
            const item = data[i];
            if (item.userOption !== "-1" && item.userOption !== item.correctOption) {
                result.push(item);
            }
        }
        return result
    }

    // Calculate overall statistics
    function calculateOverallStats() {
        const totalQuestions = questions.length;
        const correctCount = correctQuestions.length;
        const incorrectCount = inCorrectQuestions.length;
        const skippedCount = skippedQuestions.length;
        const attemptedCount = correctCount + incorrectCount;
        const accuracy = attemptedCount > 0 ? Math.round((correctCount / attemptedCount) * 100) : 0;

        // Calculate total time (if available)
        let totalTime = 0;
        if (typeof userTotalTime !== 'undefined' && userTotalTime) {
            totalTime = userTotalTime;
        } else {
            // Fallback: sum up individual question times
            resultData.forEach(q => {
                if (q.userTime) {
                    totalTime += parseFloat(q.userTime);
                }
            });
        }

        const minutes = Math.floor(totalTime / 60);
        const seconds = Math.floor(totalTime % 60);

        return {
            score: correctCount + "/" + totalQuestions,
            attempted: attemptedCount + "/" + totalQuestions,
            accuracy: accuracy + "%",
            time: minutes + "m " + seconds + "s",
            correctCount,
            incorrectCount,
            skippedCount,
            totalQuestions
        };
    }

    // Calculate sectional statistics
    function calculateSectionalStats() {
        const sectionStats = {};

        resultData.forEach(q => {
            const section = q.subject || 'General';
            if (!sectionStats[section]) {
                sectionStats[section] = {
                    total: 0,
                    correct: 0,
                    incorrect: 0,
                    skipped: 0,
                    totalTime: 0
                };
            }

            sectionStats[section].total++;

            if (q.userOption === "-1") {
                sectionStats[section].skipped++;
            } else if (q.userOption === q.correctOption) {
                sectionStats[section].correct++;
            } else {
                sectionStats[section].incorrect++;
            }

            if (q.userTime) {
                sectionStats[section].totalTime += parseFloat(q.userTime);
            }
        });

        // Convert to array format for table display
        const sectionalArray = [];
        Object.keys(sectionStats).forEach(section => {
            const stats = sectionStats[section];
            const attempted = stats.correct + stats.incorrect;
            const accuracy = attempted > 0 ? Math.round((stats.correct / attempted) * 100) : 0;
            const minutes = Math.floor(stats.totalTime / 60);
            const seconds = Math.floor(stats.totalTime % 60);

            sectionalArray.push({
                section: section,
                score: stats.correct,
                attempted: attempted,
                accuracy: accuracy + "%",
                time: minutes + "m"
            });
        });

        return sectionalArray;
    }

    // Populate the UI with calculated data
    function populateQuizResultUI() {
        resultData = getResultData();
        skippedQuestions = getSkippedQuestions(resultData);
        correctQuestions = getCorrectQuestions(resultData);
        inCorrectQuestions = getInCorrectQuestions(resultData);

        const overallStats = calculateOverallStats();
        const sectionalStats = calculateSectionalStats();

        // Update overall summary
        document.getElementById('overall-score').textContent = overallStats.score;
        document.getElementById('overall-attempted').textContent = overallStats.attempted;
        document.getElementById('overall-accuracy').textContent = overallStats.accuracy;
        document.getElementById('overall-time').textContent = overallStats.time;

        // Update question distribution
        document.getElementById('correct-count').textContent = overallStats.correctCount;
        document.getElementById('incorrect-count').textContent = overallStats.incorrectCount;
        document.getElementById('skipped-count').textContent = overallStats.skippedCount;

        // Update progress bar
        const total = overallStats.totalQuestions;
        const correctPercent = (overallStats.correctCount / total) * 100;
        const incorrectPercent = (overallStats.incorrectCount / total) * 100;
        const skippedPercent = (overallStats.skippedCount / total) * 100;

        document.getElementById('correct-progress').style.width = correctPercent + "%";
        document.getElementById('incorrect-progress').style.width = incorrectPercent + "%";
        document.getElementById('skipped-progress').style.width = skippedPercent + "%";

        // Update sectional summary table
        const tbody = document.getElementById('sectional-summary-tbody');
        tbody.innerHTML = '';

        sectionalStats.forEach(section => {
            const row = document.createElement('tr');
            row.innerHTML =
                "<td class='section-name'>" + section.section + "</td>" +
                "<td class='text-center'>" + section.score + "</td>" +
                "<td class='text-center'>" + section.attempted + "</td>" +
                "<td class='text-center'>" + section.accuracy + "</td>" +
                "<td class='text-center'>" + section.time + "</td>";
            tbody.appendChild(row);
        });
    }

    // Initialize the UI when the page loads
    document.addEventListener('DOMContentLoaded', function() {
        populateQuizResultUI();
    });

    // Also call immediately in case DOMContentLoaded has already fired
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', populateQuizResultUI);
    } else {
        populateQuizResultUI();
    }

    // Toggle between summary and solutions views
    function showSolutions() {
        document.querySelector('.quiz-summary-sections').classList.add('hidden');
        document.querySelector('.solutions-section').classList.add('active');
        loadSolutions();
    }

    function showSummary() {
        document.querySelector('.quiz-summary-sections').classList.remove('hidden');
        document.querySelector('.solutions-section').classList.remove('active');
    }

    // Load solutions content
    function loadSolutions() {
        const solutionsList = document.getElementById('solutions-list');
        solutionsList.innerHTML = '';

        if (resultData && resultData.length > 0) {
            resultData.forEach((question, index) => {
                const solutionCard = createSolutionCard(question, index + 1);
                solutionsList.appendChild(solutionCard);
            });
        } else {
            solutionsList.innerHTML = '<div class="alert alert-warning">No solutions available.</div>';
        }
    }

    // Create individual solution card
    function createSolutionCard(question, questionNumber) {
        const card = document.createElement('div');
        card.className = 'card mb-3';

        const userAnswer = question.userOption;
        const correctAnswer = question.correctOption;
        const isCorrect = userAnswer === correctAnswer;
        const isSkipped = userAnswer === "-1";

        let statusClass = 'text-warning';
        let statusText = 'Skipped';
        let statusIcon = 'fas fa-minus-circle';

        if (!isSkipped) {
            if (isCorrect) {
                statusClass = 'text-success';
                statusText = 'Correct';
                statusIcon = 'fas fa-check-circle';
            } else {
                statusClass = 'text-danger';
                statusText = 'Incorrect';
                statusIcon = 'fas fa-times-circle';
            }
        }

        card.innerHTML =
            "<div class='card-header d-flex justify-content-between align-items-center'>" +
                "<h6 class='mb-0'>Question " + questionNumber + "</h6>" +
                "<span class='" + statusClass + "'>" +
                    "<i class='" + statusIcon + "'></i> " + statusText +
                "</span>" +
            "</div>" +
            "<div class='card-body'>" +
                "<div class='question-text mb-3'>" +
                    "<strong>Question:</strong> " + (question.ps || 'Question text not available') +
                "</div>" +

                (!isSkipped ?
                "<div class='user-answer mb-2'>" +
                    "<strong>Your Answer:</strong> " +
                    "<span class='" + (isCorrect ? 'text-success' : 'text-danger') + "'>" +
                        getUserAnswerText(question, userAnswer) +
                    "</span>" +
                "</div>" : '') +

                "<div class='correct-answer mb-2'>" +
                    "<strong>Correct Answer:</strong> " +
                    "<span class='text-success'>" +
                        getUserAnswerText(question, correctAnswer) +
                    "</span>" +
                "</div>" +

                (question.explanation ?
                "<div class='explanation'>" +
                    "<strong>Explanation:</strong>" +
                    "<div class='mt-2 p-3 bg-light rounded'>" +
                        question.answerDescription +
                    "</div>" +
                "</div>" : '') +
            "</div>";

        return card;
    }

    // Get answer text based on option
    function getUserAnswerText(question, option) {
        const optionMap = {
            'A': question.op1,
            'B': question.op2,
            'C': question.op3,
            'D': question.op4
        };
        if(question.op5){
            optionMap['E'] = question.op5;
        }
        return optionMap[option] || option || 'Not available';
    }

</script>