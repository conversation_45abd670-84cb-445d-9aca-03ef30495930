package com.wonderslate.data

import groovy.transform.AutoClone;

@AutoClone
class ChaptersMst {
    Long bookId
    String name
    Date dateCreated
    String status
    Double cost
    String currency
    Integer sortOrder
    String coverImage1
    String coverImage2
    String chapterDesc
    String previewChapter
    String mcqsExtracted
    String metaData
    String gptCompleted

    static constraints = {
        status blank:true, nullable: true
        cost blank:true, nullable: true
        currency blank:true, nullable: true
        sortOrder blank:true, nullable: true
        coverImage1 blank:true, nullable: true
        coverImage2 blank:true, nullable: true
        chapterDesc blank:true, nullable: true
        previewChapter blank:true, nullable: true
        mcqsExtracted blank:true, nullable: true
        metaData blank:true, nullable: true
        gptCompleted blank:true, nullable: true
    }
}
