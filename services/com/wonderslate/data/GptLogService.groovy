package com.wonderslate.data

import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.wonderslate.cache.DataProviderService
import com.wonderslate.institute.InstitutePromptMst
import com.wonderslate.log.GptDefaultCreateLog
import com.wonderslate.log.GptLog
import com.wonderslate.publish.BooksPermission
import com.wonderslate.sqlutil.SafeSql
import com.wonderslate.usermanagement.User
import grails.converters.JSON
import grails.transaction.Transactional
@Transactional
class GptLogService {

    def redisService
    def grailsApplication
    DataProviderService dataProviderService
    def saveGptLog(GptLog gptLog) {
        String response = gptLog.response
        if(response.length()>60000){
            gptLog.response = response.substring(0,60000)

            if(response.length()>120000)
            gptLog.response2 = response.substring(60001,120000)
            else gptLog.response2 = response.substring(60001)
        }
        if (gptLog.save(flush: true)) {
            return gptLog
        } else {
            throw new Exception("Failed to save GptLog")
        }
    }

    def findGptLogs(String username, int max, int offset, String resId, String quizObjId) {
        if(quizObjId!=null){
            return GptLog.findAllByUsernameAndQuizObjId(username, quizObjId, [max: max, offset: offset, sort: 'id', order: 'desc'])
        }else{
            return GptLog.findAllByUsernameAndResId(username, resId, [max: max, offset: offset, sort: 'id', order: 'desc'])
        }
    }
    def findDriveGptLogs(String username, int max, int offset, String materialId) {
        return GptLog.findAllByUsernameAndMaterialId(username, materialId, [max: max, offset: offset, sort: 'id', order: 'desc'])
    }

    def getGPTResources(Long readingMaterialResId, boolean isTeacher = false){
        // Fetch and sort the Prompts data
        List<Prompts> sortedPrompts = Prompts.list(sort: 'sortOrder')
        ResourceDtl resourceDtl = ResourceDtl.findById(readingMaterialResId)
        String language = null
        if(resourceDtl!=null) {
            ChaptersMst chaptersMst = ChaptersMst.findById(resourceDtl.chapterId)
            if (chaptersMst != null) {
                BooksMst booksMst = BooksMst.findById(chaptersMst.bookId)
                if (booksMst != null) {
                    language = booksMst.language
                }
            }
        }
        List<GptDefaultCreateLog> resources = GptDefaultCreateLog.findAllByReadingMaterialResId(readingMaterialResId)
        List resourcesList = resources.collect { resource ->
            String promptType =  resource.promptType
            def promptDtl = sortedPrompts.find { it.promptType == promptType }
            def iconPath = null
            if(promptDtl){
                iconPath = promptDtl.iconPath
            }
            PromptsLangMst promptLang = PromptsLangMst.findByPromptTypeAndLanguage(promptType,language)
            String promptLabel = resource.promptLabel
            if(promptLang!=null){
                promptLabel = promptLang.promptLabel+" ("+resource.promptLabel+")"
            }
            return [id:resource.id,
                    promptType: resource.promptType,
                    promptLabel:promptLabel,
                    prompt:resource.prompt,
                    iconPath:iconPath,
                    iconId:promptDtl!=null?promptDtl.id:"",
                    sortOrder:promptDtl!=null?promptDtl.sortOrder:0,
            ]
        }
        if (!isTeacher) {
            resourcesList = resourcesList.findAll { !it.promptType.startsWith("teacher_") }
        }
        // Sort the resourcesList based on the sortOrder
        resourcesList.sort { it.sortOrder }

        Gson gson = new Gson();
        String element = gson.toJson(resourcesList,new TypeToken<List>() {}.getType())
        redisService.("gptResources_"+readingMaterialResId+"_"+isTeacher) = element
    }

    def getDefaultPromptList(boolean isTeacher = false){
        List promptList = Prompts.findAllByIsDefault("Yes")
        if (!isTeacher) {
            promptList = promptList.findAll { !it.promptType.startsWith("teacher_") }
        }
        promptList.sort { it.sortOrder }
        Gson gson = new Gson();
        String element = gson.toJson(promptList,new TypeToken<List>() {}.getType())
        redisService.("defaultPrompts_"+isTeacher) = element
    }
    //get default prompts of language
    def getDefaultPromptListLanguage(String language,boolean isTeacher = false){

        String sql ="select prompt_type,prompt_label,base_prompt,icon_path,sort_order,is_default,id from prompts where is_default='Yes'"
        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql);


        List promptList = results.collect{ prompt ->
            PromptsLangMst promptLang = PromptsLangMst.findByPromptTypeAndLanguage(prompt.prompt_type,language)
            String promptLabel = prompt.prompt_label
            if(promptLang!=null){
               promptLabel = promptLang.promptLabel+" ("+prompt.prompt_label+")"
            }
            return [promptType: prompt.prompt_type,
                    promptLabel:promptLabel,
                    basePrompt:prompt.base_prompt,
                    iconPath:prompt.icon_path,
                    sortOrder:prompt.sort_order,
                    isDefault:prompt.is_default,
                    id:prompt.id
            ]
        }

        if (!isTeacher) {
            promptList = promptList.findAll { !it.promptType.startsWith("teacher_") }
        }
        // Sort the resourcesList based on the sortOrder
        promptList.sort { it.sortOrder }
        Gson gson = new Gson();
        String element = gson.toJson(promptList,new TypeToken<List>() {}.getType())
        redisService.("defaultPrompts_"+language+"_"+isTeacher) = element
    }

    //get default prompts of language
    def getDefaultPromptListForResource(String resId, String bookId, boolean isTeacher = false, Long instituteId = null,String redisKey){
       println("getDefaultPromptListForResource parameters "+resId+" "+bookId+" "+isTeacher+" "+instituteId)
        BooksMst booksMst
        Long chapterId = null
        if(bookId!=null){
            booksMst = dataProviderService.getBooksMst(new Long(bookId))
        }else if(resId!=null){
            ResourceDtl resourceDtl = dataProviderService.getResourceDtl(new Long(resId))
            if(resourceDtl!=null) {
                ChaptersMst chaptersMst = ChaptersMst.findById(resourceDtl.chapterId)
                if (chaptersMst != null) {
                    chapterId = chaptersMst.id
                    booksMst = BooksMst.findById(chaptersMst.bookId)
                }
            }
        }

        // Get language information
        String language = null
        if(booksMst != null && booksMst.language != null && !"English".equals(booksMst.language)) {
            language = booksMst.language
        }

        // Initialize the prompt list
        List promptList = []

        // Check if institute-specific prompts should be used
        boolean useInstitutePrompts = false

        if (instituteId != null) {
            // Determine userType based on isTeacher flag
            String userType = isTeacher ? "Teacher" : "Student"

            // Get institute-specific prompts
            def institutePrompts = InstitutePromptMst.findAllByInstituteIdAndUserType(instituteId, userType, [sort: 'sortOrder', order: 'asc'])

            if (institutePrompts && !institutePrompts.isEmpty()) {
                useInstitutePrompts = true

                // Get corresponding prompt details from Prompts table
                promptList = institutePrompts.collect { institutePrompt ->
                    Prompts prompt = Prompts.findByPromptType(institutePrompt.promptType)
                    if (prompt) {
                        PromptsLangMst promptLang = PromptsLangMst.findByPromptTypeAndLanguage(prompt.promptType, language)
                        String promptLabel = prompt.promptLabel
                        if(promptLang != null){
                            promptLabel = promptLang.promptLabel + " (" + prompt.promptLabel + ")"
                        }

                        return [promptType: prompt.promptType,
                                promptLabel: promptLabel,
                                basePrompt: prompt.basePrompt,
                                iconPath: prompt.iconPath,
                                sortOrder: institutePrompt.sortOrder ?: prompt.sortOrder, // Use institute sort order if available
                                isDefault: prompt.isDefault,
                                id: prompt.id
                        ]
                    }
                    return null
                }.findAll { it != null } // Remove any null entries
            }
        }

        println("***** Found ${promptList.size()} prompts for institute ${instituteId}")
        println("useInstitutePrompts: $useInstitutePrompts")

        // If no institute-specific prompts or instituteId is null, use default logic
        if (!useInstitutePrompts) {
            String sql ="select prompt_type,prompt_label,base_prompt,icon_path,sort_order,is_default,id from prompts where is_default='Yes'"
            def dataSource = grailsApplication.mainContext.getBean('dataSource')
            def sql1 = new SafeSql(dataSource)
            def results = sql1.rows(sql);

            promptList = results.collect{ prompt ->
                PromptsLangMst promptLang = PromptsLangMst.findByPromptTypeAndLanguage(prompt.prompt_type, language)
                String promptLabel = prompt.prompt_label
                if(promptLang != null){
                    promptLabel = promptLang.promptLabel + " (" + prompt.prompt_label + ")"
                }
                return [promptType: prompt.prompt_type,
                        promptLabel: promptLabel,
                        basePrompt: prompt.base_prompt,
                        iconPath: prompt.icon_path,
                        sortOrder: prompt.sort_order,
                        isDefault: prompt.is_default,
                        id: prompt.id
                ]
            }

            // Only filter teacher prompts if not using institute-specific prompts
            if (!isTeacher) {
                promptList = promptList.findAll { !it.promptType.startsWith("teacher_") }
            }

            // Sort the resourcesList based on the sortOrder
            promptList.sort { it.sortOrder }
        }

        // Add MCQ and QA prompts if they exist for the chapter (for both default and institute-specific prompts)
        if(chapterId != null) {
            // Check if MCQS are created
            if(checkResourceExists(chapterId, "Multiple Choice Questions")) {
                // Add the mcq prompt if it doesn't already exist in the list
                Prompts mcqPrompt = Prompts.findByPromptType("mcq")
                if(mcqPrompt && !promptList.any { it.promptType == "mcq" }) {
                    PromptsLangMst promptLang = PromptsLangMst.findByPromptTypeAndLanguage(mcqPrompt.promptType, language)
                    String promptLabel = mcqPrompt.promptLabel
                    if(promptLang != null) {
                        promptLabel = promptLang.promptLabel + " (" + mcqPrompt.promptLabel + ")"
                    }

                    promptList.add([promptType: mcqPrompt.promptType,
                            promptLabel: promptLabel,
                            basePrompt: mcqPrompt.basePrompt,
                            iconPath: mcqPrompt.iconPath,
                            sortOrder: mcqPrompt.sortOrder,
                            isDefault: mcqPrompt.isDefault,
                            id: mcqPrompt.id
                    ])
                }
            }

            // Check if QnA are created
            if(checkResourceExists(chapterId, "QA")) {
                // Add the qna prompt if it doesn't already exist in the list
                Prompts qnaPrompt = Prompts.findByPromptType("qna")
                if(qnaPrompt && !promptList.any { it.promptType == "qna" }) {
                    PromptsLangMst promptLang = PromptsLangMst.findByPromptTypeAndLanguage(qnaPrompt.promptType, language)
                    String promptLabel = qnaPrompt.promptLabel
                    if(promptLang != null) {
                        promptLabel = promptLang.promptLabel + " (" + qnaPrompt.promptLabel + ")"
                    }

                    promptList.add([promptType: qnaPrompt.promptType,
                            promptLabel: promptLabel,
                            basePrompt: qnaPrompt.basePrompt,
                            iconPath: qnaPrompt.iconPath,
                            sortOrder: qnaPrompt.sortOrder,
                            isDefault: qnaPrompt.isDefault,
                            id: qnaPrompt.id
                    ])
                }
            }

            // Re-sort the list after adding MCQ and QA prompts
            promptList.sort { it.sortOrder }
        }

        // Cache the results in Redis
        Gson gson = new Gson();
        String element = gson.toJson(promptList, new TypeToken<List>() {}.getType())
        redisService.(""+redisKey) = element
        return promptList
    }
    boolean checkResourceExists(Long chapterId,String promptType){
        return ResourceDtl.findByChapterIdAndResTypeAndGptResourceTypeIsNotNull(chapterId,promptType)!=null
    }
    def updateGPTLog(params){
        GptLog gptLog = GptLog.findById(params.gptId)
        if(gptLog!=null){
            if(params.feedback!=null&&!"".equals(params.feedback)) gptLog.feedback = params.feedback
            if(params.feedbackType!=null&&!"".equals(params.feedbackType)) gptLog.feedbackType = params.feedbackType
            gptLog.save(flush: true)
            return "Updated"
        }else
            return "Not found"
    }

    def getBooksPermission(Long bookId,String username){

        BooksPermission booksPermission = null
        try {
            booksPermission = redisService.memoizeDomainObject(BooksPermission, "booksPermission_" + bookId+"_"+username, 3600) {
                return BooksPermission.findByBookIdAndUsername(bookId, username)
            }
        }catch (Exception e){

        }

        return booksPermission
    }
    def getChatTokensBalance(String username, Long bookId,session){
        int chatTokensBalance = 0
        BooksPermission booksPermission = getBooksPermission(bookId,username)
        if(booksPermission!=null&&booksPermission.chatTokensBalance!=null) {
            chatTokensBalance = booksPermission.chatTokensBalance
        }

        User user = dataProviderService.getUserMst(username)
        if(user!=null&&user.chatTokensBalance!=null) {
            chatTokensBalance += user.chatTokensBalance
        }
        session['chatTokensBalance'] = new Integer(chatTokensBalance)
        return chatTokensBalance
    }

    def updateTokenUsage(String username,Integer resId){
        ResourceDtl resourceDtl = dataProviderService.getResourceDtl(resId)
        ChaptersMst chaptersMst = dataProviderService.getChaptersMst(resourceDtl.chapterId)
        BooksPermission booksPermission = getBooksPermission(chaptersMst.bookId,username)
        String status = "Success"
        //first check if booksPermission is not null and then check if chatTokensBalance is not null
        if(booksPermission!=null&&booksPermission.chatTokensBalance!=null) {
            if(booksPermission.chatTokensBalance>0){
                booksPermission.chatTokensBalance = booksPermission.chatTokensBalance - 1
            }else if(booksPermission.chatTokensBalance<0){
                booksPermission.chatTokensBalance = 0
            }
            booksPermission.save(flush: true)
        }else {
            User user = dataProviderService.getUserMst(username)
            //check if user is not null and then check if user.chatTokensBalance is not null
            if(user!=null&&user.chatTokensBalance!=null) {
                if(user.chatTokensBalance>0){
                    user.chatTokensBalance = user.chatTokensBalance - 1
                }else if(user.chatTokensBalance<0){
                    user.chatTokensBalance = 0
                }
                user.save(flush: true)
            }else{
                status = "Failed"
            }
        }
        return status
    }
}
